#!/bin/bash

# Export environment variables
export MONGO_URL=mongodb://royalia:<EMAIL>/royalia?authSource=royalia
export BASE_URL=http://localhost:3001
export USER_IMAGE_PATH=/cdn/storage/images/

# AWS S3 Configuration
export AWS_IAM_USER=royalia-upload-user
export AWS_ACCESS_KEY=********************
export AWS_SECRET_ACCESS_KEY=mH4aOf3V1YRaTkxUeBdkA5tIvUoV1f6ZGneKRT6k
export AWS_S3_BUCKET_NAME=royalia

export BUNNYNET_WRITE_KEY=745189af-8265-4fc8-a5c4a6f4b90e-6915-499b
export BUNNYNET_READ_KEY=0672f546-4081-4d9a-a148f25adc0d-d726-495b
export BUNNYNET_STORAGE_ENDPOINT=storage.bunnycdn.com
export BUNNYNET_STORAGE_ZONE=royalia

# Start Meteor
meteor
