import { Meteor } from 'meteor/meteor';

// Only run on server
if (Meteor.isServer) {
  const { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
  const { Upload } = require('@aws-sdk/lib-storage');
  const crypto = require('crypto');

  // Debug environment variables
  console.log('AWS S3 Environment Variables:');
  console.log('AWS_ACCESS_KEY:', process.env.AWS_ACCESS_KEY ? 'SET' : 'NOT SET');
  console.log('AWS_SECRET_ACCESS_KEY:', process.env.AWS_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET');
  console.log('AWS_S3_BUCKET_NAME:', process.env.AWS_S3_BUCKET_NAME);

  // AWS S3 Configuration
  const S3_CONFIG = {
    region: process.env.AWS_REGION || 'us-east-1', // Use environment region or default
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  };

  console.log('S3 Region:', S3_CONFIG.region);

  const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME || 'royalia';

  // Initialize S3 client
  const s3Client = new S3Client(S3_CONFIG);

  console.log('S3 client initialized successfully');

  /**
   * Generate a unique filename for uploaded images
   * @param {string} originalName - Original filename
   * @param {string} userId - User ID for organization
   * @param {string} folder - Folder path (e.g., 'cover', 'profile')
   * @returns {string} - Unique filename with folder path
   */
  const generateUniqueFilename = (originalName, userId, folder = '') => {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = originalName.split('.').pop().toLowerCase();
    const filename = `${userId}/${timestamp}-${randomString}.${extension}`;

    // Add folder prefix if specified
    return folder ? `${folder}/${filename}` : filename;
  };

  /**
   * Get public URL for S3 object
   * @param {string} key - File key in S3
   * @returns {string} - Public URL
   */
  const getS3PublicUrl = (key) => {
    // Construct S3 public URL
    return `https://${BUCKET_NAME}.s3.amazonaws.com/${key}`;
  };

  /**
   * Upload file to AWS S3
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} filename - Filename
   * @param {string} contentType - MIME type
   * @param {string} userId - User ID
   * @param {string} folder - Folder path (e.g., 'cover', 'profile')
   * @returns {Promise<Object>} - Upload result with key and url
   */
  const uploadToS3 = async (fileBuffer, filename, contentType, userId, folder = '') => {
    try {
      const key = generateUniqueFilename(filename, userId, folder);

      const uploadParams = {
        Bucket: BUCKET_NAME,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
        // Removed ACL to avoid 403 errors - bucket should be configured for public access via bucket policy
      };

      const upload = new Upload({
        client: s3Client,
        params: uploadParams,
      });

      const result = await upload.done();

      return {
        key: key,
        url: getS3PublicUrl(key),
        location: result.Location,
        etag: result.ETag,
      };
    } catch (error) {
      console.error('Error uploading to S3:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        code: error.Code,
        statusCode: error.$metadata?.httpStatusCode,
        bucket: BUCKET_NAME,
        key: key
      });
      throw new Meteor.Error('upload-failed', `Failed to upload file to S3: ${error.message}`);
    }
  };

  /**
   * Delete file from AWS S3
   * @param {string} key - File key in S3
   * @returns {Promise<void>}
   */
  const deleteFromS3 = async (key) => {
    try {
      const deleteParams = {
        Bucket: BUCKET_NAME,
        Key: key,
      };

      await s3Client.send(new DeleteObjectCommand(deleteParams));
      console.log(`Successfully deleted ${key} from S3`);
    } catch (error) {
      console.error('Error deleting from S3:', error);
      throw new Meteor.Error('delete-failed', 'Failed to delete file from S3');
    }
  };

  /**
   * Check if file exists in S3
   * @param {string} key - File key in S3
   * @returns {Promise<boolean>} - Whether file exists
   */
  const fileExistsInS3 = async (key) => {
    try {
      const params = {
        Bucket: BUCKET_NAME,
        Key: key,
      };

      await s3Client.send(new GetObjectCommand(params));
      return true;
    } catch (error) {
      if (error.name === 'NoSuchKey') {
        return false;
      }
      throw error;
    }
  };

  /**
   * Validate file type for image uploads
   * @param {string} contentType - MIME type
   * @returns {boolean} - Whether file type is allowed
   */
  const isValidImageType = (contentType) => {
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp'
    ];
    return allowedTypes.includes(contentType.toLowerCase());
  };

  /**
   * Validate file size
   * @param {number} size - File size in bytes
   * @returns {boolean} - Whether file size is allowed
   */
  const isValidFileSize = (size) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    return size <= maxSize;
  };

  // Export functions globally for server use
  global.S3Storage = {
    uploadToS3,
    deleteFromS3,
    fileExistsInS3,
    isValidImageType,
    isValidFileSize,
    getS3PublicUrl
  };
}
