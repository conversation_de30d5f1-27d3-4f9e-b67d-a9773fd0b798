import { Meteor } from 'meteor/meteor';

// Only run on server
if (Meteor.isServer) {
  const BunnyStorageSDK = require('@bunny.net/storage-sdk');
  const crypto = require('crypto');

  // Debug environment variables
  console.log('Bunny.net Storage Environment Variables:');
  console.log('BUNNYNET_WRITE_KEY:', process.env.BUNNYNET_WRITE_KEY ? 'SET' : 'NOT SET');
  console.log('BUNNYNET_READ_KEY:', process.env.BUNNYNET_READ_KEY ? 'SET' : 'NOT SET');
  console.log('BUNNYNET_STORAGE_ENDPOINT:', process.env.BUNNYNET_STORAGE_ENDPOINT);
  console.log('BUNNYNET_STORAGE_ZONE:', process.env.BUNNYNET_STORAGE_ZONE);

  // Bunny.net Storage Configuration
  const STORAGE_ZONE = process.env.BUNNYNET_STORAGE_ZONE || 'royalia';
  const STORAGE_ENDPOINT = process.env.BUNNYNET_STORAGE_ENDPOINT || 'storage.bunnycdn.com';
  const WRITE_KEY = process.env.BUNNYNET_WRITE_KEY;
  const READ_KEY = process.env.BUNNYNET_READ_KEY;

  // Initialize Bunny.net storage zone connection
  const storageZone = BunnyStorageSDK.zone.connect_with_accesskey(
    BunnyStorageSDK.regions.StorageRegion.Falkenstein, // Default region
    STORAGE_ZONE,
    WRITE_KEY
  );

  console.log('Bunny.net storage zone connected successfully');

  /**
   * Generate a unique filename for uploaded images
   * @param {string} originalName - Original filename
   * @param {string} userId - User ID (not used in filename for security)
   * @param {string} folder - Folder path (e.g., 'cover', 'profile')
   * @returns {string} - Unique filename with folder path
   */
  const generateUniqueFilename = (originalName, userId, folder = '') => {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = originalName.split('.').pop().toLowerCase();
    const filename = `${timestamp}-${randomString}.${extension}`;

    // Add folder prefix if specified (directly under folder)
    return folder ? `${folder}/${filename}` : filename;
  };

  /**
   * Get authenticated URL for Bunny.net storage object
   * @param {string} key - File key in Bunny.net storage
   * @returns {string} - Authenticated storage URL
   */
  const getBunnyPublicUrl = (key) => {
    // Use the storage endpoint with authentication instead of CDN
    return `https://${STORAGE_ENDPOINT}/${STORAGE_ZONE}/${key}`;
  };

  /**
   * Upload file to Bunny.net Storage
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} filename - Filename
   * @param {string} contentType - MIME type
   * @param {string} userId - User ID
   * @param {string} folder - Folder path (e.g., 'cover', 'profile')
   * @returns {Promise<Object>} - Upload result with key and url
   */
  const uploadToBunny = async (fileBuffer, filename, contentType, userId, folder = '') => {
    try {
      const key = generateUniqueFilename(filename, userId, folder);

      // Upload to Bunny.net storage using the SDK
      const result = await BunnyStorageSDK.file.upload(storageZone, key, fileBuffer);

      return {
        key: key,
        url: getBunnyPublicUrl(key),
        result: result
      };
    } catch (error) {
      console.error('Error uploading to Bunny.net:', error);
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        statusCode: error.statusCode,
        storageZone: STORAGE_ZONE,
        key: key
      });
      throw new Meteor.Error('upload-failed', `Failed to upload file to Bunny.net: ${error.message}`);
    }
  };

  /**
   * Delete file from Bunny.net Storage
   * @param {string} key - File key in Bunny.net storage
   * @returns {Promise<void>}
   */
  const deleteFromBunny = async (key) => {
    try {
      await BunnyStorageSDK.file.delete(storageZone, key);
      console.log(`Successfully deleted ${key} from Bunny.net storage`);
    } catch (error) {
      console.error('Error deleting from Bunny.net:', error);
      throw new Meteor.Error('delete-failed', 'Failed to delete file from Bunny.net storage');
    }
  };

  /**
   * Check if file exists in Bunny.net Storage
   * @param {string} key - File key in Bunny.net storage
   * @returns {Promise<boolean>} - Whether file exists
   */
  const fileExistsInBunny = async (key) => {
    try {
      await BunnyStorageSDK.file.download(storageZone, key);
      return true;
    } catch (error) {
      if (error.statusCode === 404) {
        return false;
      }
      throw error;
    }
  };

  /**
   * Validate file type for image uploads
   * @param {string} contentType - MIME type
   * @returns {boolean} - Whether file type is allowed
   */
  const isValidImageType = (contentType) => {
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp'
    ];
    return allowedTypes.includes(contentType.toLowerCase());
  };

  /**
   * Validate file size
   * @param {number} size - File size in bytes
   * @returns {boolean} - Whether file size is allowed
   */
  const isValidFileSize = (size) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    return size <= maxSize;
  };

  /**
   * Download file from Bunny.net Storage with authentication
   * @param {string} key - File key in Bunny.net storage
   * @returns {Promise<Buffer>} - File buffer
   */
  const downloadFromBunny = async (key) => {
    try {
      const fileBuffer = await BunnyStorageSDK.file.download(storageZone, key);
      return fileBuffer;
    } catch (error) {
      console.error('Error downloading from Bunny.net:', error);
      throw new Meteor.Error('download-failed', 'Failed to download file from Bunny.net storage');
    }
  };

  // Export functions globally for server use
  global.BunnyStorage = {
    uploadToBunny,
    deleteFromBunny,
    fileExistsInBunny,
    downloadFromBunny,
    isValidImageType,
    isValidFileSize,
    getBunnyPublicUrl
  };
}
